package GreedyApproach;

import java.util.*;

class partition {
    public int arrayPairSum(int[] nums) {
        int n = nums.length;
        Arrays.sort(nums);
        int ans = 0;

        for (int i = 0; i < n; i += 2) {
            ans += nums[i];
        }
        return ans;
    }

    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);

        System.out.print("Enter the number of elements (must be even): ");
        int n = sc.nextInt();

        // Validate that n is even
        if (n % 2 != 0) {
            System.out.println("Error: Number of elements must be even for array pair sum!");
            sc.close();
            return;
        }

        int[] nums = new int[n];
        System.out.println("Enter " + n + " integers:");

        for (int i = 0; i < n; i++) {
            nums[i] = sc.nextInt();
        }

        partition solution = new partition();
        int result = solution.arrayPairSum(nums);

        System.out.println("Maximum sum of pairs: " + result);

        // Display the pairs for better understanding
        Arrays.sort(nums);
        System.out.print("Pairs formed: ");
        for (int i = 0; i < n; i += 2) {
            System.out.print("(" + nums[i] + "," + nums[i + 1] + ")");
            if (i + 2 < n)
                System.out.print(", ");
        }
        System.out.println();

        sc.close();
    }
}